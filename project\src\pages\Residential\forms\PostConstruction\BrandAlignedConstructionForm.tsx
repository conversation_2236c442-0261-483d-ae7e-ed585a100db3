import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Home, Sparkles, CheckCircle, ArrowRight, ChevronLeft,
  Building2, Warehouse, Wind, Sun, Paintbrush2,
  HardHat, Wrench, Hammer, DraftingCompass, AlertCircle, X
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { useBookingSubmission } from '../../../../hooks/useBookingSubmission';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

interface FormData {
  // Project details
  projectType: string;
  propertySize: string;
  scopeOfWork: string[];
  projectDescription: string;

  // Contact information (standardized structure)
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;

  // Schedule
  preferredStartDate: string;
  projectTimeline: string;

  // Additional
  specialInstructions: string;
  howDidYouHear: string;
  newsletter: boolean;
  serviceType: string;
  fileUpload: File | null;
}

interface ValidationErrors {
  [key: string]: string;
}

const BrandAlignedConstructionForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Use standardized booking submission
  const {
    state: submissionState,
    submitBooking,
    processPayment,
    loadSavedFormData,
    isLoading: isSubmitting,
    error: submissionError
  } = useBookingSubmission('construction', user, {
    saveFormData: true,
    redirectToLogin: true,
    openPaymentModal: true
  });

  const [formData, setFormData] = useState<Partial<FormData>>({
    projectType: '',
    propertySize: '',
    scopeOfWork: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredStartDate: '',
    projectTimeline: '',
    specialInstructions: '',
    howDidYouHear: '',
    newsletter: false,
    serviceType: 'construction',
    projectDescription: '',
    fileUpload: null,
  });

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  

  // Load saved form data on component mount
  useEffect(() => {
    const savedData = loadSavedFormData();
    if (savedData) {
      setFormData(savedData);
    }
  }, [loadSavedFormData]);

  const steps = [
    { id: 1, name: 'Project Details' },
    { id: 2, name: 'Scope & Timeline' },
    { id: 3, name: 'Project Description' },
    { id: 4, name: 'Contact Information' },
  ];

  // Construction project types
  const projectTypes = [
    { 
      id: 'new-construction', 
      name: 'New Construction', 
      icon: Building2, 
      description: 'Building a new structure from the ground up.'
    },
    { 
      id: 'renovation', 
      name: 'Renovation', 
      icon: Home, 
      description: 'Updating or remodeling an existing space.'
    },
    { 
      id: 'addition', 
      name: 'Home Addition', 
      icon: Warehouse, 
      description: 'Adding new rooms or structures to a property.'
    },
    { 
      id: 'demolition', 
      name: 'Demolition', 
      icon: HardHat, 
      description: 'Tearing down existing structures.'
    }
  ];

  // Project sizes with commercial/construction pricing
  const projectSizes = [
    { id: 'small', name: 'Small Project', description: 'e.g., Bathroom remodel, small deck.' },
    { id: 'medium', name: 'Medium Project', description: 'e.g., Kitchen renovation, single room addition.' },
    { id: 'large', name: 'Large Project', description: 'e.g., Major home renovation, new floor addition.' },
    { id: 'xl', name: 'Extra Large Project', description: 'e.g., Full home new construction, commercial building.' }
  ];

  // Construction-specific services for scope of work
  const scopeOfWorkOptions = [
    {
      id: 'framing',
      name: 'Framing',
      description: 'Structural framework of the building.',
      icon: Hammer,
      recommended: true
    },
    {
      id: 'roofing',
      name: 'Roofing',
      description: 'Installation and repair of roofs.',
      icon: Home,
      recommended: true
    },
    {
      id: 'plumbing',
      name: 'Plumbing',
      description: 'Piping, fixtures, and drainage systems.',
      icon: Wrench
    },
    {
      id: 'electrical',
      name: 'Electrical',
      description: 'Wiring, outlets, and electrical panels.',
      icon: Sun
    },
    {
      id: 'hvac',
      name: 'HVAC',
      description: 'Heating, ventilation, and air conditioning systems.',
      icon: Wind
    },
    {
      id: 'interior-finishes',
      name: 'Interior Finishes',
      description: 'Drywall, painting, flooring, and trim.',
      icon: Paintbrush2
    }
  ];

  const timelineOptions = [
    { id: 'flexible', name: 'Flexible' },
    { id: '1-3-months', name: '1-3 Months' },
    { id: '3-6-months', name: '3-6 Months' },
    { id: '6+ months', name: '6+ Months' }
  ];

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName':
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      case 'email':
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      case 'phone':
        return !validatePhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      case 'address':
        return value.length < 5 ? 'Please enter a complete address' : '';
      case 'city':
        return value.length < 2 ? 'Please enter a valid city' : '';
      case 'zipCode':
        return !validateZipCode(value) ? 'Please enter a valid ZIP code (12345 or 12345-6789)' : '';
      case 'projectDescription':
        return value.length < 20 ? 'Must be at least 20 characters' : '';
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Real-time validation
    const error = validateField(field, value);
    setValidationErrors(prev => ({
      ...prev,
      [field]: error
    }));
  };

  // Validate all fields and show errors
  const validateAllFields = (data: Partial<FormData>) => {
    const errors: ValidationErrors = {};
    const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode'];

    requiredFields.forEach(field => {
      const value = data[field as keyof FormData] as string;
      const error = validateField(field, value || '');
      if (error) {
        errors[field] = error;
      }
    });

    // Validate project description
    if (!data.projectDescription || data.projectDescription.length < 20) {
      errors.projectDescription = 'Project description must be at least 20 characters';
    }

    // Validate scope of work
    if (!data.scopeOfWork || data.scopeOfWork.length === 0) {
      errors.scopeOfWork = 'Please select at least one service';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Step validation
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.projectType && formData.propertySize);
      case 2:
        return !!(formData.preferredStartDate && formData.projectTimeline && formData.scopeOfWork && formData.scopeOfWork.length > 0);
      case 3:
        return !!(formData.projectDescription && formData.projectDescription.length > 20);
      case 4: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode'];
        return requiredFields.every(field => {
          const value = formData[field as keyof FormData] as string;
          return value && !validateField(field, value);
        });
      }
      default:
        return false;
    }
  };

  // Calculate estimated price for construction quote
  const calculateEstimatedPrice = () => {
    let basePrice = 500; // Base construction cleaning price

    // Adjust based on project size
    const sizeMultipliers = {
      'small': 1,
      'medium': 1.5,
      'large': 2.5,
      'xl': 4
    };

    const multiplier = sizeMultipliers[formData.propertySize as keyof typeof sizeMultipliers] || 1;
    basePrice *= multiplier;

    // Add scope of work pricing
    const scopePricing = {
      'framing': 100,
      'roofing': 150,
      'plumbing': 100,
      'electrical': 100,
      'hvac': 150,
      'interior-finishes': 200
    };

    const scopeTotal = (formData.scopeOfWork || []).reduce((total, scope) => {
      return total + (scopePricing[scope as keyof typeof scopePricing] || 0);
    }, 0);

    return Math.round(basePrice + scopeTotal);
  };

  // Transform form data to standardized format matching BookingService expectations
  const transformFormData = () => {
    return {
      // Contact information (standardized structure)
      contact: {
        firstName: formData.firstName || '',
        lastName: formData.lastName || '',
        email: formData.email || '',
        phone: formData.phone || ''
      },

      // Property details (standardized structure)
      property_details: {
        propertyType: formData.projectType,
        propertySize: formData.propertySize,
        address: formData.address,
        city: formData.city,
        zipCode: formData.zipCode,
        projectType: formData.projectType
      },

      // Service details (standardized structure)
      service_details: {
        serviceType: 'construction',
        scopeOfWork: formData.scopeOfWork || [],
        projectDescription: formData.projectDescription,
        projectTimeline: formData.projectTimeline,
        specialRequests: formData.scopeOfWork || []
      },

      // Schedule (standardized structure)
      schedule: {
        preferredDate: formData.preferredStartDate,
        preferredTime: 'flexible',
        timeline: formData.projectTimeline
      },

      // Additional standardized fields
      serviceType: 'construction',
      totalPrice: calculateEstimatedPrice(),
      specialInstructions: formData.specialInstructions,
      howDidYouHear: formData.howDidYouHear,
      newsletter: formData.newsletter,
      requestType: 'quote',

      // Metadata
      metadata: {
        submittedAt: new Date().toISOString(),
        requestType: 'quote',
        formVersion: '2.0',
        source: 'construction_form'
      }
    };
  };

  // Handle payment completion
  const handlePaymentComplete = () => {
    setShowPaymentModal(false);
    navigate('/thank-you', {
      state: {
        formData: transformFormData(),
        paymentStatus: 'paid',
        serviceType: 'Construction Project Quote',
        bookingDetails: {
          id: submissionState.booking?.id,
          type: 'Construction Project Quote',
          serviceType: 'Construction Project Quote',
          status: 'confirmed',
          totalPrice: calculateEstimatedPrice(),
          message: 'Your construction project quote has been submitted successfully! You\'ll receive a detailed estimate within 24 hours.'
        }
      }
    });
  };

  // Handle form submission with standardized flow
  const handleSubmit = async () => {
    try {
      // Validate current step
      if (!isStepValid(4)) {
        // Validate all fields to show specific errors
        validateAllFields(formData);
        return;
      }

      // Transform form data to standardized format
      const standardizedFormData = transformFormData();

      // Submit booking using standardized service
      await submitBooking(standardizedFormData, () => setShowPaymentModal(true));

    } catch (error) {
      console.error('Form submission error:', error);
      // Error is already handled by the hook
    }
  };

  const handleScopeOfWorkToggle = (scopeId: string) => {
    const currentScope = formData.scopeOfWork || [];
    if (currentScope.includes(scopeId)) {
      setFormData({
        ...formData,
        scopeOfWork: currentScope.filter(id => id !== scopeId)
      });
    } else {
      setFormData({
        ...formData,
        scopeOfWork: [...currentScope, scopeId]
      });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFormData({ ...formData, fileUpload: e.target.files[0] });
    }
  };


  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-6xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <HardHat className="w-10 h-10 text-green-400" />
              </motion.div>
              <h1 className="text-3xl sm:text-4xl font-bold text-white">
                Construction Project Quote
              </h1>
            </div>
            <p className="text-gray-200">Get a professional estimate for your next construction project.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= step.id ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-green-500 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="lg:col-span-2">
              <motion.div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sm:p-8" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
                <AnimatePresence mode="wait">
                  {/* Step 1: Property Details */}
                  {currentStep === 1 && (
                    <motion.div key="step1" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">Tell us about your project</h2>
                      
                      {/* Project Type Selection */}
                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-4">Project Type</label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          {projectTypes.map((type) => {
                            const IconComponent = type.icon;
                            return (
                              <motion.button
                                key={type.id}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, projectType: type.id, propertySize: '' })}
                                className={`relative p-4 rounded-2xl border-2 transition-all text-center ${
                                  formData.projectType === type.id
                                    ? 'bg-green-500/20 border-green-400/30 shadow-md'
                                    : 'bg-white/5 border-white/20 hover:border-white/30'
                                }`}
                              >
                                <div className={`${
                                  formData.projectType === type.id ? 'text-green-400' : 'text-white/80'
                                } mb-2 flex justify-center`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <h3 className="font-semibold text-white text-sm">{type.name}</h3>
                                <p className="text-xs text-white/80 mt-1">{type.description}</p>
                              </motion.button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Project Size Selection */}
                      {formData.projectType && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-8"
                        >
                          <label className="block text-sm font-semibold text-white mb-4">Approximate Project Size</label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {projectSizes.map((size) => (
                              <motion.button
                                key={size.id}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, propertySize: size.id })}
                                className={`relative p-6 rounded-2xl border-2 transition-all text-left ${
                                  formData.propertySize === size.id
                                    ? 'bg-green-500/20 border-green-400/30 shadow-md'
                                    : 'bg-white/5 border-white/20 hover:border-white/30'
                                }`}
                              >
                                <div className="flex items-start gap-4">
                                  <div className={`${
                                    formData.propertySize === size.id ? 'text-green-400' : 'text-white/80'
                                  }`}>
                                    <DraftingCompass className="w-8 h-8" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-start justify-between mb-2">
                                      <h3 className="font-semibold text-white text-lg">{size.name}</h3>
                                    </div>
                                    <p className="text-sm text-white/80 mb-1">{size.description}</p>
                                  </div>
                                </div>
                              </motion.button>
                            ))}
                          </div>
                        </motion.div>
                      )}

                      {/* Construction specifics */}
                      {formData.projectType && formData.propertySize && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-8 p-6 bg-green-500/10 rounded-2xl border border-green-400/30"
                        >
                          <h3 className="font-semibold text-white mb-3 flex items-center gap-2">
                            <HardHat className="w-5 h-5 text-green-400" />
                            Our Commitment to Quality
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-white/80">
                            {[
                              'Licensed & Insured Professionals',
                              'Transparent Quoting', 
                              'High-Quality Materials',
                              'On-Time Project Completion',
                              'Regular Progress Updates',
                              'Satisfaction Guarantee'
                            ].map((feature, index) => (
                              <div key={index} className="flex items-center gap-2">
                                <CheckCircle className="w-4 h-4 text-green-400" />
                                <span>{feature}</span>
                              </div>
                            ))}
                          </div>
                        </motion.div>
                      )}

                      <div className="flex justify-end">
                        <Button 
                          onClick={() => setCurrentStep(2)} 
                          disabled={!isStepValid(1)}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 2: Scope & Timeline */}
                  {currentStep === 2 && (
                    <motion.div key="step2" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">Scope of Work & Timeline</h2>
                      
                       {/* Scope of Work */}
                       <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-3">What services do you need? (Select all that apply) *</label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                          {scopeOfWorkOptions.map((service) => {
                            const IconComponent = service.icon;
                            return (
                              <motion.button
                                key={service.id}
                                onClick={() => handleScopeOfWorkToggle(service.id)}
                                className={`p-4 rounded-xl border-2 text-left transition-all ${
                                  (formData.scopeOfWork || []).includes(service.id)
                                    ? 'bg-green-500/20 border-green-400/30'
                                    : 'bg-white/5 border-white/20 hover:border-white/30'
                                }`}
                              >
                                {service.recommended && (
                                  <span className="absolute -top-2 right-4 bg-green-500 text-white text-xs px-2 py-0.5 rounded-full">
                                    Common
                                  </span>
                                )}
                                <div className="flex items-center gap-4">
                                  <div className="text-green-400">
                                    <IconComponent className="w-6 h-6" />
                                  </div>
                                  <div className="flex-1">
                                    <h3 className="font-semibold text-white">{service.name}</h3>
                                    <p className="text-sm text-white/80">{service.description}</p>
                                  </div>
                                </div>
                              </motion.button>
                            );
                          })}
                        </div>
                        {(!formData.scopeOfWork || formData.scopeOfWork.length === 0) && validationErrors.scopeOfWork && (
                          <p className="text-red-400 text-sm mt-1">Please select at least one service</p>
                        )}
                      </div>

                      {/* Date Selection */}
                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-3">Preferred Start Date</label>
                        <input
                          type="date"
                          value={formData.preferredStartDate}
                          onChange={(e) => setFormData({ ...formData, preferredStartDate: e.target.value })}
                          min={new Date().toISOString().split('T')[0]}
                          className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        />
                      </div>

                      {/* Time Selection */}
                      {formData.preferredStartDate && (
                        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="mb-6">
                          <label className="block text-sm font-semibold text-white mb-3">Estimated Project Timeline</label>
                          <GlassmorphismSelect
                            options={timelineOptions}
                            value={formData.projectTimeline}
                            onChange={(value) => setFormData({ ...formData, projectTimeline: value })}
                            placeholder="Select estimated timeline"
                          />
                        </motion.div>
                      )}

                      <div className="flex justify-between">
                        <Button 
                          variant="outline" 
                          onClick={() => setCurrentStep(1)}
                          className="border-white/30 text-white hover:bg-white/10"
                        >
                          <ChevronLeft className="mr-2 w-5 h-5" />
                          Back
                        </Button>
                        <Button 
                          onClick={() => setCurrentStep(3)} 
                          disabled={!isStepValid(2)}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 3: Project Description */}
                  {currentStep === 3 && (
                    <motion.div key="step3" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">Project Description</h2>
                      <p className="text-white/80 mb-6">Please provide as much detail as possible about your project.</p>
                      
                      {/* Project Description */}
                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-3">Project Details *</label>
                        <textarea
                          value={formData.projectDescription}
                          onChange={(e) => handleInputChange('projectDescription', e.target.value)}
                          placeholder="Describe your project, including goals, specific requirements, and any other relevant information..."
                          className={`w-full p-4 bg-white/10 border rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none ${
                            validationErrors.projectDescription ? 'border-red-400' : 'border-white/20'
                          }`}
                          rows={6}
                        />
                         {validationErrors.projectDescription && (
                              <p className="text-red-400 text-sm mt-1">{validationErrors.projectDescription}</p>
                            )}
                      </div>

                       {/* File Upload */}
                       <div className="mb-6">
                        <label className="block text-sm font-semibold text-white mb-3">Upload Plans or Photos (optional)</label>
                        <input
                          type="file"
                          onChange={handleFileChange}
                          className="w-full text-sm text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-500 file:text-white hover:file:bg-green-600"
                        />
                      </div>
                      
                      <div className="flex justify-between">
                        <Button 
                          variant="outline" 
                          onClick={() => setCurrentStep(2)}
                          className="border-white/30 text-white hover:bg-white/10"
                        >
                          <ChevronLeft className="mr-2 w-5 h-5" />
                          Back
                        </Button>
                        <Button 
                          onClick={() => setCurrentStep(4)}
                          disabled={!isStepValid(3)}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 4: Contact & Schedule */}
                  {currentStep === 4 && (
                    <motion.div key="step4" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-white mb-6">Complete your booking</h2>
                      
                      {/* Contact Form */}
                      <div className="space-y-6 mb-8">
                        {/* Name Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-semibold text-white mb-2">First name *</label>
                            <input
                              type="text"
                              value={formData.firstName}
                              onChange={(e) => handleInputChange('firstName', e.target.value)}
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="Enter your first name"
                            />
                            {validationErrors.firstName && (
                              <p className="text-red-400 text-sm mt-1">{validationErrors.firstName}</p>
                            )}
                          </div>
                          <div>
                            <label className="block text-sm font-semibold text-white mb-2">Last name *</label>
                            <input
                              type="text"
                              value={formData.lastName}
                              onChange={(e) => handleInputChange('lastName', e.target.value)}
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="Enter your last name"
                            />
                            {validationErrors.lastName && (
                              <p className="text-red-400 text-sm mt-1">{validationErrors.lastName}</p>
                            )}
                          </div>
                        </div>

                        {/* Contact Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-semibold text-white mb-2">Email address *</label>
                            <input
                              type="email"
                              value={formData.email}
                              onChange={(e) => handleInputChange('email', e.target.value)}
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="<EMAIL>"
                            />
                            {validationErrors.email && (
                              <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                            )}
                          </div>
                          <div>
                            <label className="block text-sm font-semibold text-white mb-2">Phone number *</label>
                            <input
                              type="tel"
                              value={formData.phone}
                              onChange={(e) => handleInputChange('phone', e.target.value)}
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="(*************"
                            />
                            {validationErrors.phone && (
                              <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                            )}
                          </div>
                        </div>

                        {/* Address Fields */}
                        <div>
                          <label className="block text-sm font-semibold text-white mb-2">Property address *</label>
                          <input
                            type="text"
                            value={formData.address}
                            onChange={(e) => handleInputChange('address', e.target.value)}
                            className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                            placeholder="123 Main Street"
                          />
                          {validationErrors.address && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-semibold text-white mb-2">City *</label>
                            <input
                              type="text"
                              value={formData.city}
                              onChange={(e) => handleInputChange('city', e.target.value)}
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="Your city"
                            />
                            {validationErrors.city && (
                              <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                            )}
                          </div>
                          <div>
                            <label className="block text-sm font-semibold text-white mb-2">ZIP code *</label>
                            <input
                              type="text"
                              value={formData.zipCode}
                              onChange={(e) => handleInputChange('zipCode', e.target.value)}
                              className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="12345"
                            />
                            {validationErrors.zipCode && (
                              <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                            )}
                          </div>
                        </div>

                        {/* Marketing */}
                        <div>
                          <label className="block text-sm font-semibold text-white mb-2">How did you hear about us? (optional)</label>
                          <GlassmorphismSelect
                            options={[
                              { id: 'google', name: 'Google Search' },
                              { id: 'referral', name: 'Friend/Family Referral' },
                              { id: 'social', name: 'Social Media' },
                              { id: 'contractor', name: 'Contractor Recommendation' },
                              { id: 'other', name: 'Other' }
                            ]}
                            value={formData.howDidYouHear}
                            onChange={(value) => setFormData({ ...formData, howDidYouHear: value })}
                            placeholder="Select an option"
                          />
                        </div>

                        {/* Newsletter */}
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            id="newsletter"
                            checked={formData.newsletter}
                            onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
                            className="w-4 h-4 text-green-600 rounded"
                          />
                          <label htmlFor="newsletter" className="text-sm text-white/80">
                            Yes, I'd like to receive cleaning tips and special offers via email
                          </label>
                        </div>
                      </div>
                      
                      <div className="flex justify-between">
                        <Button 
                          variant="outline" 
                          onClick={() => setCurrentStep(3)}
                          className="border-white/30 text-white hover:bg-white/10"
                        >
                          <ChevronLeft className="mr-2 w-5 h-5" />
                          Back
                        </Button>
                        <Button 
                          onClick={handleSubmit} 
                          disabled={!isStepValid(4) || isSubmitting}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isSubmitting ? 'Submitting...' : 'Request a Quote'}
                          {!isSubmitting && <CheckCircle className="ml-2 w-5 h-5" />}
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </div>

            {/* Price Summary Sidebar */}
            <div className="lg:sticky lg:top-8 h-fit">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Quote Summary</h3>
                  <div className="flex items-center gap-1 text-xs bg-green-500/20 text-green-300 px-2 py-1 rounded-full border border-green-400/30">
                    <Sparkles className="w-3 h-3 fill-current" />
                    <span>Professional Quote</span>
                  </div>
                </div>

                {/* Price Display */}
                {formData.projectType && formData.propertySize && (
                  <div className="mb-4 p-4 bg-green-500/10 rounded-xl border border-green-400/30">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-white">${calculateEstimatedPrice()}</p>
                      <p className="text-sm text-white/80">Estimated Quote</p>
                      <p className="text-xs text-white/60 mt-1">Final price may vary based on project specifics</p>
                    </div>
                  </div>
                )}
                
                {formData.projectType ? (
                  <>
                    <div className="space-y-3 mb-4">
                        <div className="text-center py-4">
                          <HardHat className="w-12 h-12 text-white/30 mx-auto mb-2" />
                          <p className="text-white/80 font-semibold">Project: {projectTypes.find(p => p.id === formData.projectType)?.name}</p>
                          <p className="text-white/60 text-sm">Size: {projectSizes.find(s => s.id === formData.propertySize)?.name}</p>
                        </div>
                    </div>

                    {/* Scope Breakdown */}
                    <div className="space-y-2 text-sm mb-4">
                      <h4 className="font-semibold text-white mb-2">Selected Scope of Work:</h4>
                      {(formData.scopeOfWork && formData.scopeOfWork.length > 0) ? (
                        (formData.scopeOfWork || []).map(scope => {
                          const service = scopeOfWorkOptions.find(s => s.id === scope);
                          return service ? (
                            <div key={scope} className="flex items-center justify-between text-white/80">
                              <span>{service.name}</span>
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            </div>
                          ) : null;
                        })
                      ) : (
                        <p className="text-white/60 text-sm">No specific scope selected yet.</p>
                      )}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <HardHat className="w-12 h-12 text-white/30 mx-auto mb-2" />
                    <p className="text-white/60">Complete the form to get your custom quote</p>
                  </div>
                )}

                {/* Trust Badges */}
                <div className="mt-6 space-y-4">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
                    <div>
                      <p className="font-medium text-white">Experienced Professionals</p>
                      <p className="text-sm text-white/80">Years of experience in the construction industry.</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <HardHat className="w-5 h-5 text-green-400 mt-0.5" />
                    <div>
                      <p className="font-medium text-white">Quality Craftsmanship</p>
                      <p className="text-sm text-white/80">Commitment to the highest standards of quality.</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculateEstimatedPrice()}
          description="Construction Project Quote"
          customerEmail={formData.email}
          formData={transformFormData()}
          user={user}
          onPaymentComplete={handlePaymentComplete}
        />
      )}

      {/* Error Display */}
      {submissionError && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 max-w-md">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="font-medium">Submission Failed</p>
              <p className="text-sm text-red-100 mt-1">{submissionError}</p>
              <button
                onClick={() => {
                  // Reset error and retry submission
                  const standardizedFormData = transformFormData();
                  submitBooking(standardizedFormData, () => setShowPaymentModal(true));
                }}
                className="mt-2 text-sm bg-red-600 hover:bg-red-700 px-3 py-1 rounded transition-colors"
              >
                Try Again
              </button>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="text-red-200 hover:text-white"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </AnimatedBackground>
  );
};

export default BrandAlignedConstructionForm;
