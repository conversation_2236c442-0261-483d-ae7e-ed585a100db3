/**
 * Construction Form Integration Tests
 * 
 * Tests the construction form's integration with the booking system
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { BookingService } from '../lib/api/bookingService';
import { User } from '@supabase/supabase-js';

// Mock user for testing
const mockUser: User = {
  id: 'user-123',
  email: '<EMAIL>',
  created_at: new Date().toISOString(),
  app_metadata: {},
  user_metadata: {},
  aud: 'authenticated',
  role: 'authenticated'
};

// Mock construction form data (flat structure as expected by validation)
const mockConstructionFormData = {
  // Contact information (flat structure)
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '555-0123',
  address: '123 Construction St',
  city: 'Builder City',
  zipCode: '12345',

  // Project details
  projectType: 'new-construction',
  propertySize: 'large',
  scopeOfWork: ['framing', 'roofing', 'interior-finishes'],
  projectDescription: 'New residential construction project requiring post-construction cleanup',
  projectTimeline: '3-6-months',
  preferredStartDate: '2024-02-15',

  // Additional fields
  serviceType: 'construction',
  totalPrice: 1500,
  specialInstructions: 'Please coordinate with site manager',
  requestType: 'quote'
};

describe('Construction Form Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Form Data Validation', () => {
    it('should validate construction form data correctly', () => {
      // First, let's check if validation rules exist for construction
      const { FormValidator } = require('../lib/utils/validation');
      const rules = FormValidator.getServiceRules('construction');
      console.log('Construction validation rules:', Object.keys(rules));

      const validation = BookingService.validateBookingData(
        mockConstructionFormData,
        'construction',
        mockUser
      );

      // Debug: log validation errors if any
      if (!validation.isValid) {
        console.log('Validation errors:', validation.errors);
        console.log('Form data keys:', Object.keys(mockConstructionFormData));
      }

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.sanitizedData).toBeDefined();
    });

    it('should handle missing required fields', () => {
      const incompleteData = {
        ...mockConstructionFormData,
        firstName: '',
        lastName: '',
        email: '',
        phone: ''
      };

      const validation = BookingService.validateBookingData(
        incompleteData,
        'construction',
        mockUser
      );

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Service-Specific Details', () => {
    it('should extract construction-specific details correctly', () => {
      const serviceDetails = BookingService.getServiceSpecificDetails(
        mockConstructionFormData,
        'construction'
      );

      expect(serviceDetails).toMatchObject({
        projectType: 'new-construction',
        propertySize: 'large',
        scopeOfWork: ['framing', 'roofing', 'interior-finishes'],
        projectDescription: expect.any(String),
        projectTimeline: '3-6-months',
        totalPrice: 1500
      });
    });
  });

  describe('Data Transformation', () => {
    it('should transform form data to standardized booking format', () => {
      const validation = BookingService.validateBookingData(
        mockConstructionFormData,
        'construction',
        mockUser
      );

      expect(validation.sanitizedData).toMatchObject({
        user_id: mockUser.id,
        service_type: 'construction',
        contact: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '555-0123'
        },
        property_details: expect.objectContaining({
          propertyType: 'new-construction',
          propertySize: 'large'
        }),
        service_details: expect.objectContaining({
          projectType: 'new-construction',
          scopeOfWork: ['framing', 'roofing', 'interior-finishes']
        }),
        schedule: expect.objectContaining({
          preferredDate: '2024-02-15'
        }),
        status: 'pending',
        payment_status: 'pending'
      });
    });
  });

  describe('Price Calculation', () => {
    it('should handle construction pricing correctly', () => {
      const validation = BookingService.validateBookingData(
        mockConstructionFormData,
        'construction',
        mockUser
      );

      expect(validation.sanitizedData?.total_price).toBe(1500);
    });
  });
});
