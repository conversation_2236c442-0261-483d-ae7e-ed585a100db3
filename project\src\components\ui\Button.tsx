import React from 'react';
import { cn } from '../../lib/utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

export function Button({
  className,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  ...props
}: ButtonProps) {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300',
        'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500',
        'disabled:opacity-50 disabled:cursor-not-allowed active:scale-[0.98]',
        'touch-manipulation', // Improve touch interactions
        {
          'bg-brand-600 text-white hover:bg-brand-700 shadow-sm hover:shadow-md': variant === 'primary',
          'bg-brand-100 text-brand-800 hover:bg-brand-200': variant === 'secondary',
          'border-2 border-brand-600 text-brand-600 hover:bg-brand-50 hover:border-brand-700 hover:text-brand-700': variant === 'outline',
          'text-sm px-3 py-1.5': size === 'sm',
          'text-base px-5 py-2': size === 'md',
          'text-lg px-6 py-3': size === 'lg',
          'w-full': fullWidth,
        },
        className
      )}
      {...props}
    />
  );
}
