import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './errorHandler';

// Input sanitization utilities
export class InputSanitizer {
  // Sanitize HTML to prevent XSS attacks
  static sanitizeHtml(input: string): string {
    if (!input) return '';

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .replace(/alert\s*\(/gi, '') // Remove alert calls
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, ''); // Remove event handlers
  }

  // Sanitize SQL input to prevent injection
  static sanitizeSql(input: string): string {
    if (!input) return '';

    // Remove or escape dangerous SQL characters
    return input
      .replace(/'/g, "''")  // Escape single quotes
      .replace(/;/g, '')    // Remove semicolons
      .replace(/--/g, '')   // Remove SQL comments
      .replace(/\/\*/g, '') // Remove block comment start
      .replace(/\*\//g, '') // Remove block comment end
      .replace(/xp_/gi, '') // Remove extended procedures
      .replace(/sp_/gi, '') // Remove stored procedures
      .replace(/drop\s+table/gi, '') // Remove DROP TABLE
      .replace(/delete\s+from/gi, '') // Remove DELETE FROM
      .replace(/insert\s+into/gi, '') // Remove INSERT INTO
      .replace(/update\s+\w+\s+set/gi, ''); // Remove UPDATE SET
  }

  // Sanitize general text input
  static sanitizeText(input: string, maxLength: number = 1000): string {
    if (!input) return '';

    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/[<>]/g, ''); // Remove angle brackets
  }

  // Sanitize email input
  static sanitizeEmail(input: string): string {
    if (!input) return '';

    return input
      .trim()
      .toLowerCase()
      .slice(0, 254) // RFC 5321 limit
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
      .replace(/[^a-z0-9@._-]/g, ''); // Only allow valid email characters
  }

  // Sanitize phone number
  static sanitizePhone(input: string): string {
    if (!input) return '';

    return input
      .replace(/[^0-9+\-\(\)\s]/g, '') // Only allow valid phone characters
      .trim()
      .slice(0, 20); // Reasonable phone number length
  }

  // Sanitize numeric input
  static sanitizeNumber(input: string | number): number | null {
    if (input === null || input === undefined || input === '') return null;

    const num = typeof input === 'string' ? parseFloat(input.replace(/[^0-9.-]/g, '')) : input;
    return isNaN(num) ? null : num;
  }
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number; // For numeric values
  max?: number; // For numeric values
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  type?: 'string' | 'number' | 'email' | 'phone' | 'url' | 'date' | 'array' | 'boolean';
  custom?: (value: any) => boolean;
  customMessage?: string;
  allowedValues?: string[]; // For enum-like validation
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export class FormValidator {
  static validateField(field: string, value: any, rules: ValidationRule): ValidationError | null {
    // Sanitize input first
    let sanitizedValue = value;
    if (typeof value === 'string') {
      sanitizedValue = InputSanitizer.sanitizeText(value);
    }

    // Required check
    if (rules.required && (!sanitizedValue || (typeof sanitizedValue === 'string' && sanitizedValue.trim() === '') || (Array.isArray(sanitizedValue) && sanitizedValue.length === 0))) {
      const error = ErrorHandler.handleFormValidationError(field, sanitizedValue, 'required');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Skip other validations if field is empty and not required
    if (!sanitizedValue || (typeof sanitizedValue === 'string' && sanitizedValue.trim() === '') || (Array.isArray(sanitizedValue) && sanitizedValue.length === 0)) {
      return null;
    }

    // Type validation
    if (rules.type) {
      const typeError = this.validateType(field, sanitizedValue, rules.type);
      if (typeError) return typeError;
    }

    // Numeric range validation
    if (rules.type === 'number' && typeof sanitizedValue === 'number') {
      if (rules.min !== undefined && sanitizedValue < rules.min) {
        return {
          field,
          message: `${field} must be at least ${rules.min}`,
          code: 'MIN_VALUE'
        };
      }
      if (rules.max !== undefined && sanitizedValue > rules.max) {
        return {
          field,
          message: `${field} must be at most ${rules.max}`,
          code: 'MAX_VALUE'
        };
      }
    }

    // Allowed values validation
    if (rules.allowedValues && !rules.allowedValues.includes(sanitizedValue)) {
      return {
        field,
        message: `${field} must be one of: ${rules.allowedValues.join(', ')}`,
        code: 'INVALID_VALUE'
      };
    }

    const stringValue = sanitizedValue.toString().trim();

    // Email validation
    if (rules.email) {
      const sanitizedEmail = InputSanitizer.sanitizeEmail(stringValue);
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(sanitizedEmail)) {
        const error = ErrorHandler.handleFormValidationError(field, sanitizedEmail, 'email');
        return {
          field,
          message: error.message,
          code: error.code
        };
      }
    }

    // Phone validation
    if (rules.phone) {
      const sanitizedPhone = InputSanitizer.sanitizePhone(stringValue);
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      const cleanPhone = sanitizedPhone.replace(/[\s\-\(\)\.]/g, '');
      if (!phoneRegex.test(cleanPhone) || cleanPhone.length < 10) {
        const error = ErrorHandler.handleFormValidationError(field, sanitizedPhone, 'phone');
        return {
          field,
          message: error.message,
          code: error.code
        };
      }
    }

    // Min length validation
    if (rules.minLength && stringValue.length < rules.minLength) {
      const error = ErrorHandler.handleFormValidationError(field, rules.minLength, 'minLength');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Max length validation
    if (rules.maxLength && stringValue.length > rules.maxLength) {
      const error = ErrorHandler.handleFormValidationError(field, rules.maxLength, 'maxLength');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(stringValue)) {
      const error = ErrorHandler.handleFormValidationError(field, value, 'pattern');
      return {
        field,
        message: error.message,
        code: error.code
      };
    }

    // Custom validation
    if (rules.custom && !rules.custom(value)) {
      const message = rules.customMessage || `${field} is invalid`;
      return {
        field,
        message,
        code: 'CUSTOM_VALIDATION_ERROR'
      };
    }

    return null;
  }

  /**
   * Validate field type
   */
  static validateType(field: string, value: any, expectedType: string): ValidationError | null {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field,
            message: `${field} must be a text value`,
            code: 'INVALID_TYPE'
          };
        }
        break;

      case 'number':
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        if (isNaN(numValue) || typeof numValue !== 'number') {
          return {
            field,
            message: `${field} must be a valid number`,
            code: 'INVALID_TYPE'
          };
        }
        break;

      case 'email':
        if (typeof value !== 'string' || !this.isValidEmail(value)) {
          return {
            field,
            message: `${field} must be a valid email address`,
            code: 'INVALID_EMAIL'
          };
        }
        break;

      case 'phone':
        if (typeof value !== 'string' || !this.isValidPhone(value)) {
          return {
            field,
            message: `${field} must be a valid phone number`,
            code: 'INVALID_PHONE'
          };
        }
        break;

      case 'date':
        const dateValue = new Date(value);
        if (isNaN(dateValue.getTime())) {
          return {
            field,
            message: `${field} must be a valid date`,
            code: 'INVALID_DATE'
          };
        }
        // Check if date is not in the past (for scheduling)
        if (field.includes('preferred') || field.includes('Date')) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          if (dateValue < today) {
            return {
              field,
              message: `${field} cannot be in the past`,
              code: 'PAST_DATE'
            };
          }
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          return {
            field,
            message: `${field} must be a list`,
            code: 'INVALID_TYPE'
          };
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          return {
            field,
            message: `${field} must be true or false`,
            code: 'INVALID_TYPE'
          };
        }
        break;

      case 'url':
        if (typeof value !== 'string' || !this.isValidUrl(value)) {
          return {
            field,
            message: `${field} must be a valid URL`,
            code: 'INVALID_URL'
          };
        }
        break;
    }

    return null;
  }

  /**
   * Enhanced email validation
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  /**
   * Enhanced phone validation
   */
  static isValidPhone(phone: string): boolean {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    // Check for valid US phone number formats
    return cleaned.length === 10 || (cleaned.length === 11 && cleaned.startsWith('1'));
  }

  /**
   * URL validation
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static validateForm(formData: Record<string, any>, validationRules: Record<string, ValidationRule>): ValidationError[] {
    const errors: ValidationError[] = [];

    for (const [field, rules] of Object.entries(validationRules)) {
      const value = formData[field];
      const error = this.validateField(field, value, rules);
      if (error) {
        errors.push(error);
      }
    }

    return errors;
  }

  static isFormValid(formData: Record<string, any>, validationRules: Record<string, ValidationRule>): boolean {
    return this.validateForm(formData, validationRules).length === 0;
  }

  // Common validation rules
  static commonRules = {
    required: { required: true },
    email: { required: true, email: true },
    phone: { required: true, phone: true },
    name: { required: true, minLength: 2, maxLength: 50 },
    address: { required: true, minLength: 5, maxLength: 200 },
    zipCode: { 
      required: true, 
      pattern: /^\d{5}(-\d{4})?$/,
      customMessage: 'Please enter a valid ZIP code (12345 or 12345-6789)'
    },
    password: {
      required: true,
      minLength: 8,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      customMessage: 'Password must contain at least 8 characters with uppercase, lowercase, number, and special character'
    }
  };

  // Service-specific validation rules - COMPREHENSIVE COVERAGE
  static serviceValidationRules = {
    // ============================================================================
    // RESIDENTIAL SERVICES
    // ============================================================================

    residential_regular: {
      // Property details
      propertyType: { required: true },
      propertySize: { required: true },
      bedrooms: { required: true, type: 'number', min: 1, max: 20 },
      bathrooms: { required: true, type: 'number', min: 1, max: 20 },

      // Schedule
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },
      frequency: { required: true },

      // Contact information
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    residential_deep: {
      // Property details
      propertyType: { required: true },
      propertySize: { required: true },
      bedrooms: { required: true, type: 'number', min: 1, max: 20 },
      bathrooms: { required: true, type: 'number', min: 1, max: 20 },

      // Deep cleaning specifics
      lastDeepClean: { required: false },
      specificAreas: { required: false, type: 'array' },

      // Schedule
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },

      // Contact information
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    residential_move: {
      // Property details
      propertyType: { required: true },
      propertySize: { required: true },
      bedrooms: { required: true, type: 'number', min: 1, max: 20 },
      bathrooms: { required: true, type: 'number', min: 1, max: 20 },

      // Move-specific details
      moveType: { required: true }, // move-in or move-out
      moveDate: { required: true, type: 'date' },

      // Schedule
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },

      // Contact information
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    pool: {
      // Pool details
      poolType: { required: true },
      poolSize: { required: true },
      poolCondition: { required: false },

      // Schedule
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },
      frequency: { required: true },

      // Contact information
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    // ============================================================================
    // COMMERCIAL SERVICES
    // ============================================================================

    office: {
      // Business details
      companyName: { required: true, minLength: 2, maxLength: 100 },
      contactName: { required: true, minLength: 2, maxLength: 50 },
      businessType: { required: false },

      // Property details
      squareFootage: { required: true, type: 'number', min: 100, max: 1000000 },
      numberOfOffices: { required: false, type: 'number', min: 1, max: 1000 },
      numberOfBathrooms: { required: false, type: 'number', min: 1, max: 100 },

      // Service details
      serviceFrequency: { required: true },
      preferredTime: { required: true },

      // Contact information
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    carpet: {
      // Carpet details
      numberOfRooms: { required: true, type: 'number', min: 1, max: 50 },
      carpetType: { required: true },
      carpetCondition: { required: false },
      totalSquareFootage: { required: false, type: 'number', min: 10, max: 10000 },

      // Service details
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },

      // Contact information
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    window: {
      // Window details
      numberOfWindows: { required: true, type: 'number', min: 1, max: 500 },
      windowType: { required: false },
      buildingHeight: { required: true },

      // Service details
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },
      frequency: { required: false },

      // Contact information
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    sanitization: {
      // Business details
      companyName: { required: true, minLength: 2, maxLength: 100 },
      contactName: { required: true, minLength: 2, maxLength: 50 },
      businessType: { required: true },

      // Property details
      squareFootage: { required: true, type: 'number', min: 100, max: 1000000 },

      // Service details
      sanitizationType: { required: true },
      urgency: { required: false },
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },

      // Contact information
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    construction: {
      // Project details
      projectType: { required: true },
      propertySize: { required: true },
      scopeOfWork: { required: true, type: 'array', minLength: 1 },
      projectDescription: { required: true, minLength: 20, maxLength: 1000 },

      // Timeline
      preferredStartDate: { required: true, type: 'date' },
      projectTimeline: { required: true },

      // Contact information (standardized with other forms)
      firstName: { required: true, minLength: 2, maxLength: 50 },
      lastName: { required: true, minLength: 2, maxLength: 50 },
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode,

      // Optional fields
      specialInstructions: { required: false, maxLength: 500 },
      howDidYouHear: { required: false },
      newsletter: { required: false, type: 'boolean' }
    },

    residential_post_construction: {
      // Property details
      propertyType: { required: true },
      propertySize: { required: true },
      constructionType: { required: true },
      cleaningServices: { required: true, type: 'array', minLength: 1 },

      // Timeline
      preferredStartDate: { required: true, type: 'date' },
      cleaningTimeline: { required: true },

      // Contact information (standardized with other forms)
      firstName: { required: true, minLength: 2, maxLength: 50 },
      lastName: { required: true, minLength: 2, maxLength: 50 },
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode,

      // Optional fields
      specialInstructions: { required: false, maxLength: 500 },
      howDidYouHear: { required: false },
      newsletter: { required: false, type: 'boolean' }
    },

    // ============================================================================
    // LEGACY SUPPORT (for backward compatibility)
    // ============================================================================

    carpetCleaning: {
      numberOfRooms: { required: true, type: 'number', min: 1, max: 50 },
      carpetType: { required: true },
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    deepCleaning: {
      propertyType: { required: true },
      propertySize: { required: true },
      numberOfBedrooms: { required: true, type: 'number', min: 1, max: 20 },
      numberOfBathrooms: { required: true, type: 'number', min: 1, max: 20 },
      preferredDate: { required: true, type: 'date' },
      preferredTime: { required: true },
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    },

    eventCleaning: {
      eventType: { required: true },
      eventSize: { required: true },
      eventDate: { required: true, type: 'date' },
      setupTime: { required: true },
      firstName: FormValidator.commonRules.name,
      lastName: FormValidator.commonRules.name,
      email: FormValidator.commonRules.email,
      phone: FormValidator.commonRules.phone,
      address: FormValidator.commonRules.address,
      city: { required: true, minLength: 2, maxLength: 50 },
      zipCode: FormValidator.commonRules.zipCode
    }
  };

  // Get validation rules for a specific service
  static getServiceRules(serviceType: string): Record<string, ValidationRule> {
    return this.serviceValidationRules[serviceType as keyof typeof this.serviceValidationRules] || {};
  }
} 