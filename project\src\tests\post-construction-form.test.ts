/**
 * Post-Construction Form Integration Tests
 * 
 * Tests the post-construction form's integration with the booking system
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { BookingService } from '../lib/api/bookingService';
import { User } from '@supabase/supabase-js';

// Mock user for testing
const mockUser: User = {
  id: 'user-123',
  email: '<EMAIL>',
  created_at: new Date().toISOString(),
  app_metadata: {},
  user_metadata: {},
  aud: 'authenticated',
  role: 'authenticated'
};

// Mock post-construction form data
const mockPostConstructionFormData = {
  // Property details
  propertyType: 'residential',
  propertySize: 'large',
  constructionType: 'new-construction',
  cleaningServices: ['debris-removal', 'dust-cleaning', 'window-cleaning'],
  
  // Contact information
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '555-0123',
  address: '123 Construction St',
  city: 'Builder City',
  zipCode: '12345',
  
  // Timeline
  preferredStartDate: '2024-02-15',
  cleaningTimeline: 'within-week',
  
  // Additional fields
  serviceType: 'residential_post_construction',
  totalPrice: 800,
  specialInstructions: 'Please coordinate with site manager'
};

describe('Post-Construction Form Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Form Data Validation', () => {
    it('should validate post-construction form data correctly', () => {
      const validation = BookingService.validateBookingData(
        mockPostConstructionFormData,
        'residential_post_construction',
        mockUser
      );

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.sanitizedData).toBeDefined();
    });

    it('should handle missing required fields', () => {
      const incompleteData = {
        ...mockPostConstructionFormData,
        firstName: '',
        lastName: '',
        email: '',
        phone: ''
      };

      const validation = BookingService.validateBookingData(
        incompleteData,
        'residential_post_construction',
        mockUser
      );

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Service-Specific Details', () => {
    it('should extract post-construction specific details correctly', () => {
      const serviceDetails = BookingService.getServiceSpecificDetails(
        mockPostConstructionFormData,
        'residential_post_construction'
      );

      expect(serviceDetails).toMatchObject({
        propertyType: 'residential',
        propertySize: 'large',
        constructionType: 'new-construction',
        cleaningServices: ['debris-removal', 'dust-cleaning', 'window-cleaning'],
        cleaningTimeline: 'within-week',
        totalPrice: 800
      });
    });
  });

  describe('Data Transformation', () => {
    it('should transform form data to standardized booking format', () => {
      const validation = BookingService.validateBookingData(
        mockPostConstructionFormData,
        'residential_post_construction',
        mockUser
      );

      expect(validation.sanitizedData).toMatchObject({
        user_id: mockUser.id,
        service_type: 'residential_post_construction',
        contact: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '555-0123'
        },
        property_details: expect.objectContaining({
          propertyType: 'residential',
          propertySize: 'large'
        }),
        service_details: expect.objectContaining({
          constructionType: 'new-construction',
          cleaningServices: ['debris-removal', 'dust-cleaning', 'window-cleaning']
        }),
        schedule: expect.objectContaining({
          preferredDate: '2024-02-15'
        }),
        status: 'pending',
        payment_status: 'pending'
      });
    });
  });

  describe('Price Calculation', () => {
    it('should handle post-construction pricing correctly', () => {
      const validation = BookingService.validateBookingData(
        mockPostConstructionFormData,
        'residential_post_construction',
        mockUser
      );

      expect(validation.sanitizedData?.total_price).toBe(800);
    });
  });
});
