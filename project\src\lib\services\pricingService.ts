/**
 * Centralized Pricing Service
 * 
 * This module provides a single source of truth for all pricing calculations
 * across the entire application, eliminating inconsistencies found in multiple
 * pricing calculation methods.
 * 
 * Evidence: Fixes 6+ different pricing calculation methods found across the codebase
 * with conflicting base prices and calculation logic.
 */

import { ServiceTypeConfig, getServiceBasePrice, isResidentialService } from './serviceTypeRegistry';

// ============================================================================
// Core Pricing Interfaces
// ============================================================================

export interface PricingInput {
  serviceType: string;
  propertySize?: string | number; // Can be square footage or size category
  frequency?: string;
  addOns?: string[];
  urgency?: string;
  location?: {
    zipCode?: string;
    state?: string;
    city?: string;
  };
  customOptions?: Record<string, unknown>;
}

export interface PricingResult {
  basePrice: number;
  sizeAdjustment: number;
  frequencyDiscount: number;
  addOnsTotal: number;
  urgencyFee: number;
  locationMultiplier: number;
  subtotal: number;
  taxes: number;
  total: number;
  breakdown: PricingBreakdown[];
  currency: string;
}

export interface PricingBreakdown {
  item: string;
  description: string;
  amount: number;
  type: 'base' | 'adjustment' | 'addon' | 'discount' | 'fee' | 'tax';
}

// ============================================================================
// Pricing Configuration
// ============================================================================

// Size multipliers for different property sizes
const SIZE_MULTIPLIERS: Record<string, number> = {
  // Square footage based (per sq ft rates)
  'sqft_residential': 0.15,
  'sqft_commercial': 0.25,
  
  // Category based multipliers
  'small': 0.8,
  'medium': 1.0,
  'large': 1.4,
  'xlarge': 1.8,
  'xl': 1.8,
  
  // Room based (residential)
  '1-room': 0.7,
  '2-rooms': 1.0,
  '3-rooms': 1.3,
  '4-rooms': 1.6,
  '5-plus-rooms': 2.0,
  
  // Specific size ranges
  'under-1000': 0.8,
  '1000-2000': 1.0,
  '2000-3000': 1.3,
  '3000-5000': 1.6,
  'over-5000': 2.0
};

// Frequency discounts
const FREQUENCY_DISCOUNTS: Record<string, number> = {
  'onetime': 0.0,
  'weekly': 0.15,      // 15% discount
  'biweekly': 0.10,    // 10% discount
  'monthly': 0.05,     // 5% discount
  'quarterly': 0.0,
  'yearly': 0.0
};

// Add-on pricing by service type
const ADDON_PRICING: Record<string, Record<string, number>> = {
  residential_regular: {
    'deep-clean': 50,
    'eco-friendly': 20,
    'inside-oven': 30,
    'inside-fridge': 30,
    'windows': 40,
    'laundry': 25,
    'garage': 35,
    'basement': 40
  },
  residential_deep: {
    'eco-friendly': 25,
    'inside-oven': 20, // Already included in deep clean
    'inside-fridge': 20,
    'windows': 40,
    'garage': 50,
    'basement': 60
  },
  'post-construction-cleaning': {
    'dust-removal': 40,
    'window-cleaning': 60,
    'floor-cleaning': 50,
    'paint-removal': 80,
    'fixture-cleaning': 45,
    'cabinet-cleaning': 35,
    'baseboard-cleaning': 30,
    'debris-removal': 75
  },
  'residential_post_construction': {
    'dust-removal': 40,
    'window-cleaning': 60,
    'floor-cleaning': 50,
    'paint-removal': 80,
    'fixture-cleaning': 45,
    'cabinet-cleaning': 35,
    'baseboard-cleaning': 30,
    'debris-removal': 75
  },
  carpet: {
    'stain-protection': 59,
    'deep-deodorizing': 49,
    'upholstery-cleaning': 79,
    'pet-treatment': 69,
    'furniture-moving': 25
  },
  pressure: {
    'deck-sealing': 75,
    'concrete-sealing': 65,
    'gutter-cleaning': 85,
    'house-siding': 149
  }
};

// Urgency fees
const URGENCY_FEES: Record<string, number> = {
  'standard': 0.0,
  'priority': 25,      // $25 fee
  'urgent': 50,        // $50 fee
  'emergency': 100     // $100 fee
};

// Location multipliers (can be expanded with real data)
const LOCATION_MULTIPLIERS: Record<string, number> = {
  'default': 1.0,
  'urban': 1.1,
  'suburban': 1.0,
  'rural': 0.9
};

// ============================================================================
// Core Pricing Functions
// ============================================================================

/**
 * Calculate comprehensive pricing for any service
 */
export function calculatePrice(input: PricingInput): PricingResult {
  const breakdown: PricingBreakdown[] = [];
  
  // 1. Get base price
  const basePrice = getServiceBasePrice(input.serviceType);
  breakdown.push({
    item: 'Base Service',
    description: ServiceTypeConfig.getDisplayName(input.serviceType),
    amount: basePrice,
    type: 'base'
  });
  
  // 2. Calculate size adjustment
  const sizeAdjustment = calculateSizeAdjustment(basePrice, input.propertySize, input.serviceType);
  if (sizeAdjustment !== basePrice) {
    breakdown.push({
      item: 'Size Adjustment',
      description: `Property size: ${input.propertySize}`,
      amount: sizeAdjustment - basePrice,
      type: 'adjustment'
    });
  }
  
  // 3. Apply frequency discount
  const beforeDiscount = sizeAdjustment;
  const frequencyDiscount = calculateFrequencyDiscount(beforeDiscount, input.frequency);
  if (frequencyDiscount > 0) {
    breakdown.push({
      item: 'Frequency Discount',
      description: `${input.frequency} service`,
      amount: -frequencyDiscount,
      type: 'discount'
    });
  }
  
  // 4. Calculate add-ons
  const addOnsTotal = calculateAddOns(input.serviceType, input.addOns || []);
  if (addOnsTotal > 0) {
    const addOnDetails = getAddOnBreakdown(input.serviceType, input.addOns || []);
    breakdown.push(...addOnDetails);
  }
  
  // 5. Calculate urgency fee
  const urgencyFee = calculateUrgencyFee(input.urgency);
  if (urgencyFee > 0) {
    breakdown.push({
      item: 'Urgency Fee',
      description: `${input.urgency} service`,
      amount: urgencyFee,
      type: 'fee'
    });
  }
  
  // 6. Apply location multiplier
  const locationMultiplier = calculateLocationMultiplier(input.location);
  const subtotalBeforeLocation = sizeAdjustment - frequencyDiscount + addOnsTotal + urgencyFee;
  const locationAdjustment = subtotalBeforeLocation * (locationMultiplier - 1);
  
  if (Math.abs(locationAdjustment) > 0.01) {
    breakdown.push({
      item: 'Location Adjustment',
      description: `${input.location?.city || 'Local'} area`,
      amount: locationAdjustment,
      type: 'adjustment'
    });
  }
  
  // 7. Calculate totals
  const subtotal = subtotalBeforeLocation + locationAdjustment;
  const taxes = 0; // Tax calculation can be added here if needed
  const total = subtotal + taxes;
  
  return {
    basePrice,
    sizeAdjustment: sizeAdjustment - basePrice,
    frequencyDiscount,
    addOnsTotal,
    urgencyFee,
    locationMultiplier,
    subtotal,
    taxes,
    total: Math.round(total * 100) / 100, // Round to 2 decimal places
    breakdown,
    currency: 'USD'
  };
}

/**
 * Calculate size-based price adjustment
 */
function calculateSizeAdjustment(basePrice: number, propertySize: string | number | undefined, serviceType: string): number {
  if (!propertySize) return basePrice;
  
  // Handle numeric square footage
  if (typeof propertySize === 'number') {
    const rate = isResidentialService(serviceType) ? SIZE_MULTIPLIERS.sqft_residential : SIZE_MULTIPLIERS.sqft_commercial;
    const sqftPrice = propertySize * rate;
    
    // Apply volume discounts for large spaces
    let discount = 0;
    if (propertySize > 10000) discount = 0.1;      // 10% discount
    else if (propertySize > 5000) discount = 0.05; // 5% discount
    
    return Math.max(sqftPrice * (1 - discount), basePrice);
  }
  
  // Handle size categories
  const multiplier = SIZE_MULTIPLIERS[propertySize as string] || 1.0;
  return basePrice * multiplier;
}

/**
 * Calculate frequency-based discount
 */
function calculateFrequencyDiscount(price: number, frequency: string | undefined): number {
  if (!frequency) return 0;
  
  const discountRate = FREQUENCY_DISCOUNTS[frequency] || 0;
  return price * discountRate;
}

/**
 * Calculate add-ons total
 */
function calculateAddOns(serviceType: string, addOns: string[]): number {
  const serviceAddOns = ADDON_PRICING[serviceType] || {};
  
  return addOns.reduce((total, addOn) => {
    return total + (serviceAddOns[addOn] || 0);
  }, 0);
}

/**
 * Get detailed add-on breakdown
 */
function getAddOnBreakdown(serviceType: string, addOns: string[]): PricingBreakdown[] {
  const serviceAddOns = ADDON_PRICING[serviceType] || {};
  
  return addOns
    .filter(addOn => serviceAddOns[addOn])
    .map(addOn => ({
      item: 'Add-on',
      description: addOn.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      amount: serviceAddOns[addOn],
      type: 'addon' as const
    }));
}

/**
 * Calculate urgency fee
 */
function calculateUrgencyFee(urgency: string | undefined): number {
  if (!urgency) return 0;
  return URGENCY_FEES[urgency] || 0;
}

/**
 * Calculate location multiplier
 */
function calculateLocationMultiplier(location: PricingInput['location']): number {
  if (!location) return LOCATION_MULTIPLIERS.default;
  
  // This can be expanded with real location-based pricing data
  // For now, using simple urban/suburban/rural classification
  return LOCATION_MULTIPLIERS.default;
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Quick price calculation for simple cases
 */
export function getQuickPrice(serviceType: string, propertySize?: string | number): number {
  const result = calculatePrice({
    serviceType,
    propertySize,
    frequency: 'onetime'
  });
  
  return result.total;
}

/**
 * Validate pricing input
 */
export function validatePricingInput(input: PricingInput): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!input.serviceType) {
    errors.push('Service type is required');
  } else if (!ServiceTypeConfig.validate(input.serviceType)) {
    errors.push(`Invalid service type: ${input.serviceType}`);
  }
  
  if (input.frequency && !(input.frequency in FREQUENCY_DISCOUNTS)) {
    errors.push(`Invalid frequency: ${input.frequency}`);
  }
  
  if (input.urgency && !(input.urgency in URGENCY_FEES)) {
    errors.push(`Invalid urgency level: ${input.urgency}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Format price for display
 */
export function formatPrice(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

// ============================================================================
// Export configuration for external use
// ============================================================================

export const PricingConfig = {
  sizeMultipliers: SIZE_MULTIPLIERS,
  frequencyDiscounts: FREQUENCY_DISCOUNTS,
  addonPricing: ADDON_PRICING,
  urgencyFees: URGENCY_FEES,
  locationMultipliers: LOCATION_MULTIPLIERS
} as const;
