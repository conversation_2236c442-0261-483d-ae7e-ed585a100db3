import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { HomePage } from './pages/Home';
import { Login } from './pages/auth/Login';
import { Register } from './pages/auth/Register';
import { AccountDashboard } from './pages/AccountDashboard';
import { NewThankYou } from './pages/ThankYou/NewThankYou';
import { About } from './pages/About';
import { Solutions } from './pages/Solutions';
import { Residential } from './pages/Residential';
import { Commercial } from './pages/Commercial';
import { Industrial } from './pages/Industrial';
import { Privacy } from './pages/Privacy';
import { Terms } from './pages/Terms';
import { Contact } from './pages/Contact';
import { ServiceAreas } from './pages/ServiceAreas';
import { ServiceFormPage } from './pages/ServiceForm';
import ModernRegularCleaningForm from './pages/Residential/forms/RegularCleaning/ModernRegularCleaningForm';
import BrandAlignedDeepCleaningForm from './pages/Residential/forms/DeepCleaning/BrandAlignedDeepCleaningForm';
import BrandAlignedCarpetForm from './pages/Residential/forms/CarpetCleaning/BrandAlignedCarpetForm';
import BrandAlignedEventForm from './pages/Residential/forms/EventCleaning/BrandAlignedEventForm';
import ModernMoveOutCleaningForm from './pages/Residential/forms/MoveOutCleaning/ModernMoveOutCleaningForm';
import TestBookingService from './pages/TestBookingService';
import BrandAlignedPostConstructionForm from './pages/Residential/forms/PostConstruction/BrandAlignedPostConstructionForm';
import BrandAlignedUpholsteryForm from './pages/Residential/forms/UpholsteryCleaning/BrandAlignedUpholsteryForm';
import BrandAlignedWindowForm from './pages/Residential/forms/WindowCleaning/BrandAlignedWindowForm';
import BrandAlignedPressureForm from './pages/Residential/forms/PressureWashing/BrandAlignedPressureForm';
import BrandAlignedSanitizationForm from './pages/Residential/forms/Sanitization/BrandAlignedSanitizationForm';
import BrandAlignedPoolForm from './pages/Residential/forms/PoolCleaning/BrandAlignedPoolForm';
import BrandAlignedChimneyForm from './pages/Residential/forms/ChimneyCleaning/BrandAlignedChimneyForm';
import BrandAlignedOfficeForm from './pages/Commercial/forms/OfficeCleaning/BrandAlignedOfficeForm';
import { default as ModernCorporateForm } from './pages/Commercial/forms/ModernCorporateForm';
import { default as ModernIndustrialForm } from './pages/Commercial/forms/ModernIndustrialForm';
import { default as BrandAlignedCommercialPostConstructionForm } from './pages/Commercial/forms/PostConstruction/BrandAlignedPostConstructionForm';
import { default as BrandAlignedWasteForm } from './pages/Commercial/forms/WasteManagement/BrandAlignedWasteForm';
import { default as BrandAlignedFloorCareForm } from './pages/Commercial/forms/FloorCare/BrandAlignedFloorCareForm';
import { default as BrandAlignedCommercialUpholsteryForm } from './pages/Commercial/forms/UpholsteryCarpetCleaning/BrandAlignedUpholsteryForm';
import { default as BrandAlignedCommercialWindowForm } from './pages/Commercial/forms/WindowCleaning/BrandAlignedWindowForm';
import { default as BrandAlignedCommercialPressureForm } from './pages/Commercial/forms/PressureWashing/BrandAlignedPressureForm';
import { default as BrandAlignedCommercialSanitizationForm } from './pages/Commercial/forms/SanitizationServices/BrandAlignedSanitizationForm';
import { default as BrandAlignedKitchenForm } from './pages/Commercial/forms/CommercialKitchen/BrandAlignedKitchenForm';
import { AuthProvider } from './lib/auth/AuthProvider';
import { ProtectedRoute } from './lib/auth/ProtectedRoute';
import { ScrollToTop } from './components/layout/ScrollToTop';
import { ToastProvider } from './components/ui/Toast';
import { AppWrapper } from './components/AppWrapper';
import { ServiceTypeTest } from './pages/ServiceTypeTest';
import CheckoutTest from './pages/CheckoutTest';
import { initializeScrollOptimizations, setupScrollRestoration } from './utils/scrollOptimization';
import './utils/testPaymentFlow'; // Import test function for browser console
import './utils/pwaUtils'; // Initialize PWA features
import './index.css';

// Initialize scroll optimizations
initializeScrollOptimizations();
setupScrollRestoration();

// Opt-in to v7 behaviors to suppress deprecation warnings
// See: https://reactrouter.com/en/main/upgrading/future
const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <>
        <ScrollToTop />
        <HomePage />
      </>
    ),
  },
  {
    path: '/commercial',
    element: (
      <>
        <ScrollToTop />
        <Commercial />
      </>
    ),
  },
  {
    path: '/commercial/office',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedOfficeForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/corporate',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <ModernCorporateForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/industrial',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <ModernIndustrialForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/post-construction',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedCommercialPostConstructionForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/waste-management',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedWasteForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/floor-care',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedFloorCareForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/upholstery-carpet',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedCommercialUpholsteryForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/window-cleaning',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedCommercialWindowForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/pressure-washing',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedCommercialPressureForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/sanitization',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedCommercialSanitizationForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/commercial/kitchen',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedKitchenForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential',
    element: (
      <>
        <ScrollToTop />
        <Residential />
      </>
    ),
  },
  {
    path: '/industrial',
    element: (
      <>
        <ScrollToTop />
        <Industrial />
      </>
    ),
  },
  {
    path: '/residential/deep',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedDeepCleaningForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/regular',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <ModernRegularCleaningForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/carpet',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedCarpetForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/event',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedEventForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/moveout',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <ModernMoveOutCleaningForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/test-booking',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <TestBookingService />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/postconstruction',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedPostConstructionForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/construction',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedPostConstructionForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/upholstery',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedUpholsteryForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/window',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedWindowForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/pressure',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedPressureForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/sanitization',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedSanitizationForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/pool',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedPoolForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/chimney',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <BrandAlignedChimneyForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/residential/:serviceId',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <ModernRegularCleaningForm />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/services',
    element: <Navigate to="/solutions" replace />
  },
  {
    path: '/solutions',
    element: (
      <>
        <ScrollToTop />
        <Solutions />
      </>
    ),
  },
  {
    path: '/service-areas',
    element: (
      <>
        <ScrollToTop />
        <ServiceAreas />
      </>
    ),
  },
  {
    path: '/service-form/:serviceId',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute requireAuth={false}>
          <ServiceFormPage />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/about',
    element: (
      <>
        <ScrollToTop />
        <About />
      </>
    ),
  },
  {
    path: '/contact',
    element: (
      <>
        <ScrollToTop />
        <Contact />
      </>
    ),
  },
  {
    path: '/auth/login',
    element: (
      <>
        <ScrollToTop />
        <Login />
      </>
    ),
  },
  {
    path: '/auth/register',
    element: (
      <>
        <ScrollToTop />
        <Register />
      </>
    ),
  },
  {
    path: '/accountdashboard',
    element: (
      <>
        <ScrollToTop />
        <ProtectedRoute>
          <AccountDashboard />
        </ProtectedRoute>
      </>
    ),
  },
  {
    path: '/thank-you',
    element: <NewThankYou />,
    },
  {
    path: '/privacy',
    element: (
      <>
        <ScrollToTop />
        <Privacy />
      </>
    ),
  },
  {
    path: '/terms',
    element: (
      <>
        <ScrollToTop />
        <Terms />
      </>
    ),
  },
  {
    path: '/service-type-test',
    element: (
      <>
        <ScrollToTop />
        <ServiceTypeTest />
      </>
    ),
  },
  {
    path: '/checkout-test',
    element: (
      <>
        <ScrollToTop />
        <CheckoutTest />
      </>
    ),
  }
], {
  future: {
    v7_relativeSplatPath: true,
    v7_fetcherPersist: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_skipActionErrorRevalidation: true
  }
});

const root = document.getElementById('root');
if (root) {
  createRoot(root).render(
    <StrictMode>
      <ToastProvider>
        <AuthProvider>
          <AppWrapper router={router} />
        </AuthProvider>
      </ToastProvider>
    </StrictMode>
  );
}